import type { Payload } from 'payload'

export const seedCMSData = async (payload: Payload): Promise<void> => {
  payload.logger.info('Seeding CMS data...')

  try {
    // First, create sample images for projects
    const sampleImageBuffer = Buffer.from(
      'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==',
      'base64'
    )

    const projectImage1 = await payload.create({
      collection: 'media',
      data: {
        filename: 'project-sample-1.png',
        mimeType: 'image/png',
        alt: 'Sample project image 1',
        category: 'projects',
      },
      file: {
        data: sampleImageBuffer,
        mimetype: 'image/png',
        name: 'project-sample-1.png',
        size: sampleImageBuffer.length,
      },
    })

    const projectImage2 = await payload.create({
      collection: 'media',
      data: {
        filename: 'project-sample-2.png',
        mimeType: 'image/png',
        alt: 'Sample project image 2',
        category: 'projects',
      },
      file: {
        data: sampleImageBuffer,
        mimetype: 'image/png',
        name: 'project-sample-2.png',
        size: sampleImageBuffer.length,
      },
    })

    // Create sample projects
    const sampleProjects = [
      {
        title: 'Indigenous Knowledge Documentation Initiative',
        description: {
          root: {
            children: [
              {
                children: [
                  {
                    detail: 0,
                    format: 0,
                    mode: 'normal',
                    style: '',
                    text: 'A comprehensive project to document and preserve traditional knowledge systems of indigenous communities in Kenya, focusing on medicinal plants, agricultural practices, and cultural heritage.',
                    type: 'text',
                    version: 1,
                  },
                ],
                direction: 'ltr',
                format: '',
                indent: 0,
                type: 'paragraph',
                version: 1,
              },
            ],
            direction: 'ltr',
            format: '',
            indent: 0,
            type: 'root',
            version: 1,
          },
        },
        summary: 'Documenting and preserving traditional knowledge systems of indigenous communities in Kenya.',
        image: {
          image: projectImage1.id,
          alt: 'Indigenous Knowledge Documentation Initiative',
          caption: 'Community members sharing traditional knowledge',
        },
        category: 'knowledge-preservation',
        pillar: 'indigenous-knowledge',
        status: 'active',
        timeline: {
          startDate: '2023-01-01',
          endDate: '2025-12-31',
          duration: '3 years',
        },
        budget: {
          totalBudget: 15000000,
          currency: 'KES',
        },
        impact: {
          beneficiaries: 5000,
          communities: 25,
          jobsCreated: 50,
        },
        featured: true,
        published: true,
        tags: [
          { tag: 'indigenous knowledge' },
          { tag: 'documentation' },
          { tag: 'preservation' },
          { tag: 'traditional medicine' },
        ],
        slug: 'indigenous-knowledge-documentation-initiative',
      },
      {
        title: 'Community-Led Natural Products Development',
        description: {
          root: {
            children: [
              {
                children: [
                  {
                    detail: 0,
                    format: 0,
                    mode: 'normal',
                    style: '',
                    text: 'Empowering local communities to develop sustainable natural products businesses based on traditional knowledge and local resources.',
                    type: 'text',
                    version: 1,
                  },
                ],
                direction: 'ltr',
                format: '',
                indent: 0,
                type: 'paragraph',
                version: 1,
              },
            ],
            direction: 'ltr',
            format: '',
            indent: 0,
            type: 'root',
            version: 1,
          },
        },
        summary: 'Empowering communities to develop sustainable natural products businesses.',
        image: {
          image: projectImage2.id,
          alt: 'Community-Led Natural Products Development',
          caption: 'Community members working on natural products',
        },
        category: 'community-empowerment',
        pillar: 'community-innovation',
        status: 'active',
        timeline: {
          startDate: '2023-06-01',
          endDate: '2026-05-31',
          duration: '3 years',
        },
        budget: {
          totalBudget: 20000000,
          currency: 'KES',
        },
        impact: {
          beneficiaries: 3000,
          communities: 15,
          jobsCreated: 75,
        },
        featured: true,
        published: true,
        tags: [
          { tag: 'community development' },
          { tag: 'natural products' },
          { tag: 'entrepreneurship' },
          { tag: 'sustainability' },
        ],
        slug: 'community-led-natural-products-development',
      },
    ]

    for (const project of sampleProjects) {
      await payload.create({
        collection: 'projects',
        data: project,
      })
    }

    // Create sample success stories
    const sampleSuccessStories = [
      {
        title: 'Aloe Vera Cooperative Transforms Rural Economy in Baringo',
        summary: "A women's cooperative in Baringo County successfully commercialized traditional aloe vera products, increasing household incomes by 300% and creating 150 jobs.",
        content: {
          root: {
            children: [
              {
                children: [
                  {
                    detail: 0,
                    format: 0,
                    mode: 'normal',
                    style: '',
                    text: "In the arid landscapes of Baringo County, the Chepkemoi Women's Cooperative has transformed traditional aloe vera knowledge into a thriving business. Through NPI support, they developed sustainable harvesting practices, quality processing methods, and market linkages that have revolutionized their community's economic prospects.",
                    type: 'text',
                    version: 1,
                  },
                ],
                direction: 'ltr',
                format: '',
                indent: 0,
                type: 'paragraph',
                version: 1,
              },
            ],
            direction: 'ltr',
            format: '',
            indent: 0,
            type: 'root',
            version: 1,
          },
        },
        image: {
          image: projectImage1.id,
          alt: 'Aloe Vera Cooperative members at work',
          focalPoint: { x: 50, y: 50 },
        },
        category: 'economic-empowerment',
        location: {
          specificLocation: 'Chepkemoi Village, Baringo County',
        },
        participants: {
          beneficiary: {
            name: 'Mary Chepkemoi',
            role: 'Cooperative Chairperson',
            organization: 'Chepkemoi Women\'s Cooperative',
          },
          knowledgeHolder: {
            name: 'Mama Grace Kiprotich',
            title: 'Traditional Healer',
            expertise: 'Aloe vera traditional uses and cultivation',
          },
        },
        impact: {
          beneficiaries: 150,
          jobsCreated: 45,
          metrics: [
            {
              metric: 'Income Increase',
              value: '300',
              unit: '%',
              description: 'Average household income increase',
            },
          ],
        },
        timeline: {
          startDate: '2022-03-01',
          completionDate: '2024-02-28',
          duration: '2 years',
        },
        investment: {
          totalAmount: 2500000,
          currency: 'KES',
        },
        featured: true,
        published: true,
        slug: 'aloe-vera-cooperative-baringo',
      },
      {
        title: 'From Traditional Healer to Certified Entrepreneur',
        summary: 'Mary Wanjiku transformed her traditional healing practice into a certified natural products business.',
        content: {
          root: {
            children: [
              {
                children: [
                  {
                    detail: 0,
                    format: 0,
                    mode: 'normal',
                    style: '',
                    text: 'Mary Wanjiku, a traditional healer from Kiambu County, has successfully transformed her ancestral knowledge into a thriving natural products business. Through NPI\'s capacity building program, she learned modern business practices while preserving traditional knowledge.',
                    type: 'text',
                    version: 1,
                  },
                ],
                direction: 'ltr',
                format: '',
                indent: 0,
                type: 'paragraph',
                version: 1,
              },
            ],
            direction: 'ltr',
            format: '',
            indent: 0,
            type: 'root',
            version: 1,
          },
        },
        category: 'economic-empowerment',
        participants: {
          beneficiary: {
            name: 'Mary Wanjiku',
            role: 'Traditional Healer & Entrepreneur',
            organization: 'Wanjiku Natural Products',
          },
          knowledgeHolder: {
            name: 'Mary Wanjiku',
            title: 'Traditional Healer',
            expertise: 'Medicinal Plants & Traditional Medicine',
          },
        },
        impact: {
          beneficiaries: 200,
          jobsCreated: 5,
          incomeIncrease: {
            percentage: 300,
            currency: 'KES',
          },
        },
        timeline: {
          startDate: '2022-03-01',
          completionDate: '2023-12-31',
          duration: '22 months',
        },
        investment: {
          totalAmount: 500000,
          currency: 'KES',
        },
        featured: true,
        published: true,
        tags: [
          { tag: 'entrepreneurship' },
          { tag: 'traditional medicine' },
          { tag: 'women empowerment' },
          { tag: 'business development' },
        ],
        slug: 'traditional-healer-to-certified-entrepreneur',
      },
    ]

    for (const story of sampleSuccessStories) {
      await payload.create({
        collection: 'success-stories',
        data: story,
      })
    }

    // Create sample events
    const sampleEvents = [
      {
        title: 'Natural Products Innovation Summit 2024',
        summary: 'Annual summit bringing together stakeholders in the natural products industry to share innovations, best practices, and forge new partnerships.',
        description: {
          root: {
            children: [
              {
                children: [
                  {
                    detail: 0,
                    format: 0,
                    mode: 'normal',
                    style: '',
                    text: 'Join us for the premier gathering of natural products industry leaders, researchers, and community representatives. This summit will showcase cutting-edge innovations, traditional knowledge preservation efforts, and sustainable development initiatives across Kenya.',
                    type: 'text',
                    version: 1,
                  },
                ],
                direction: 'ltr',
                format: '',
                indent: 0,
                type: 'paragraph',
                version: 1,
              },
            ],
            direction: 'ltr',
            format: '',
            indent: 0,
            type: 'root',
            version: 1,
          },
        },
        type: 'conference',
        category: 'knowledge-sharing',
        status: 'upcoming',
        schedule: {
          startDate: '2024-09-15',
          endDate: '2024-09-17',
          startTime: '08:00 AM',
          endTime: '06:00 PM',
          timezone: 'EAT',
        },
        location: {
          venue: 'Kenyatta International Convention Centre',
          address: 'Harambee Avenue, Nairobi',
          isVirtual: false,
        },
        registration: {
          required: true,
          deadline: '2024-09-01',
          capacity: 500,
          fee: {
            amount: 5000,
            currency: 'KES',
          },
        },
        image: {
          image: projectImage2.id,
          alt: 'Natural Products Innovation Summit',
          focalPoint: { x: 50, y: 50 },
        },
        featured: true,
        published: true,
        slug: 'natural-products-innovation-summit-2024',
      },
    ]

    for (const event of sampleEvents) {
      await payload.create({
        collection: 'events',
        data: event,
      })
    }



    // Create sample resources
    const sampleResources = [
      {
        title: 'Traditional Medicine Business Development Guide',
        description: {
          root: {
            children: [
              {
                children: [
                  {
                    detail: 0,
                    format: 0,
                    mode: 'normal',
                    style: '',
                    text: 'A comprehensive guide for traditional healers and practitioners looking to develop sustainable businesses while preserving indigenous knowledge.',
                    type: 'text',
                    version: 1,
                  },
                ],
                direction: 'ltr',
                format: '',
                indent: 0,
                type: 'paragraph',
                version: 1,
              },
            ],
            direction: 'ltr',
            format: '',
            indent: 0,
            type: 'root',
            version: 1,
          },
        },
        summary: 'Comprehensive guide for traditional healers to develop sustainable businesses.',
        type: 'training-guide',
        category: 'indigenous-knowledge',
        metadata: {
          authors: [
            {
              name: 'Dr. Jane Muthoni',
              organization: 'Natural Products Institute',
              role: 'Senior Researcher',
            },
          ],
          publishDate: '2023-08-15',
          version: '2.0',
          language: 'en',
          pageCount: 45,
          fileSize: '2.3 MB',
        },
        access: {
          level: 'public',
          requiresRegistration: false,
        },
        analytics: {
          downloadCount: 0,
          viewCount: 0,
        },
        keywords: [
          { keyword: 'traditional medicine' },
          { keyword: 'business development' },
          { keyword: 'entrepreneurship' },
          { keyword: 'indigenous knowledge' },
        ],
        featured: true,
        published: true,
        slug: 'traditional-medicine-business-development-guide',
      },
    ]

    for (const resource of sampleResources) {
      await payload.create({
        collection: 'resources',
        data: resource,
      })
    }

    // Create sample news articles
    const sampleNews = [
      {
        title: 'NPI Launches New Indigenous Knowledge Documentation Program',
        subtitle: 'Preserving Traditional Wisdom for Future Generations',
        summary: 'The Natural Products Institute announces a groundbreaking initiative to document and preserve indigenous knowledge systems across Kenya.',
        content: {
          root: {
            children: [
              {
                children: [
                  {
                    detail: 0,
                    format: 0,
                    mode: 'normal',
                    style: '',
                    text: 'NAIROBI, Kenya - The Natural Products Institute (NPI) today announced the launch of its comprehensive Indigenous Knowledge Documentation Program, a three-year initiative aimed at preserving traditional knowledge systems across Kenya\'s diverse communities.',
                    type: 'text',
                    version: 1,
                  },
                ],
                direction: 'ltr',
                format: '',
                indent: 0,
                type: 'paragraph',
                version: 1,
              },
            ],
            direction: 'ltr',
            format: '',
            indent: 0,
            type: 'root',
            version: 1,
          },
        },
        category: 'news',
        status: 'published',
        publishDate: '2024-01-15',
        author: {
          name: 'Communications Team',
          role: 'Communications Manager',
          organization: 'Natural Products Institute',
        },
        engagement: {
          allowComments: true,
          socialSharing: true,
          newsletter: true,
        },
        analytics: {
          viewCount: 0,
          shareCount: 0,
        },
        featured: true,
        urgent: false,
        tags: [
          { tag: 'indigenous knowledge' },
          { tag: 'documentation' },
          { tag: 'program launch' },
          { tag: 'preservation' },
        ],
        slug: 'npi-launches-indigenous-knowledge-documentation-program',
      },
    ]

    for (const article of sampleNews) {
      await payload.create({
        collection: 'news',
        data: article,
      })
    }

    // Create sample investment opportunities
    const sampleInvestmentOpportunities = [
      {
        title: 'Natural Cosmetics Manufacturing Facility',
        description: {
          root: {
            children: [
              {
                children: [
                  {
                    detail: 0,
                    format: 0,
                    mode: 'normal',
                    style: '',
                    text: 'Investment opportunity to establish a state-of-the-art natural cosmetics manufacturing facility utilizing indigenous plant-based ingredients.',
                    type: 'text',
                    version: 1,
                  },
                ],
                direction: 'ltr',
                format: '',
                indent: 0,
                type: 'paragraph',
                version: 1,
              },
            ],
            direction: 'ltr',
            format: '',
            indent: 0,
            type: 'root',
            version: 1,
          },
        },
        summary: 'Establish a natural cosmetics manufacturing facility using indigenous ingredients.',
        sector: 'cosmetics-personal-care',
        investmentType: 'equity',
        status: 'open',
        financial: {
          fundingRequired: 50000000,
          currency: 'KES',
          expectedReturns: {
            roi: 25,
            paybackPeriod: '4-5 years',
          },
        },
        businessModel: {
          valueProposition: {
            root: {
              children: [
                {
                  children: [
                    {
                      detail: 0,
                      format: 0,
                      mode: 'normal',
                      style: '',
                      text: 'Unique natural cosmetics products based on traditional Kenyan ingredients with proven efficacy and cultural significance.',
                      type: 'text',
                      version: 1,
                    },
                  ],
                  direction: 'ltr',
                  format: '',
                  indent: 0,
                  type: 'paragraph',
                  version: 1,
                },
              ],
              direction: 'ltr',
              format: '',
              indent: 0,
              type: 'root',
              version: 1,
            },
          },
          targetMarket: {
            root: {
              children: [
                {
                  children: [
                    {
                      detail: 0,
                      format: 0,
                      mode: 'normal',
                      style: '',
                      text: 'Growing natural and organic cosmetics market in East Africa and international export markets.',
                      type: 'text',
                      version: 1,
                    },
                  ],
                  direction: 'ltr',
                  format: '',
                  indent: 0,
                  type: 'paragraph',
                  version: 1,
                },
              ],
              direction: 'ltr',
              format: '',
              indent: 0,
              type: 'root',
              version: 1,
            },
          },
        },
        location: {
          counties: [], // Will be populated with actual county IDs
          specificLocation: 'Kiambu County Industrial Area',
        },
        impact: {
          beneficiaries: 1000,
          jobsCreated: 150,
        },
        featured: true,
        urgent: false,
        tags: [
          { tag: 'cosmetics' },
          { tag: 'manufacturing' },
          { tag: 'natural products' },
          { tag: 'investment' },
        ],
        slug: 'natural-cosmetics-manufacturing-facility',
      },
    ]

    for (const opportunity of sampleInvestmentOpportunities) {
      await payload.create({
        collection: 'investment-opportunities',
        data: opportunity,
      })
    }

    payload.logger.info('CMS data seeding completed successfully!')
  } catch (error) {
    payload.logger.error('Error seeding CMS data:', error)
    throw error
  }
}
