'use client'

import React from 'react'
import {
  NPISection,
  NPISectionHeader,
  NPISectionTitle,
  NPISectionDescription,
} from '@/components/ui/npi-section'
import { NPICard, NPICardHeader, NPICardTitle, NPICardContent } from '@/components/ui/npi-card'
import { NPIButton } from '@/components/ui/npi-button'
import Image from 'next/image'
import Link from 'next/link'
import { motion } from 'framer-motion'
import {
  ArrowLeft,
  MapPin,
  Calendar,
  Users,
  DollarSign,
  Target,
  Handshake,
  Building,
  Clock,
  TrendingUp,
  CheckCircle,
} from 'lucide-react'
import type { CMSProject } from '@/lib/cms/types'

interface NPIProjectDetailsClientProps {
  project: any // Use any for now to handle Payload CMS response structure
}

// Helper function to extract text from Lexical rich text
const extractTextFromLexical = (lexicalData: any): string => {
  if (!lexicalData || !lexicalData.root || !lexicalData.root.children) {
    return ''
  }

  const extractFromChildren = (children: any[]): string => {
    return children
      .map((child) => {
        if (child.type === 'text') {
          return child.text || ''
        } else if (child.children) {
          return extractFromChildren(child.children)
        }
        return ''
      })
      .join(' ')
  }

  return extractFromChildren(lexicalData.root.children)
}

const getStatusColor = (status: string) => {
  switch (status) {
    case 'active':
      return 'bg-[#25718A] text-white'
    case 'completed':
      return 'bg-[#8A3E25] text-white'
    case 'planning':
      return 'bg-[#725242] text-white'
    case 'on-hold':
      return 'bg-yellow-600 text-white'
    case 'cancelled':
      return 'bg-red-600 text-white'
    default:
      return 'bg-[#725242] text-white'
  }
}

const getCategoryColor = (category: string) => {
  const colors = [
    { bg: 'bg-[#8A3E25]', text: 'text-white', border: 'border-[#8A3E25]' },
    { bg: 'bg-[#25718A]', text: 'text-white', border: 'border-[#25718A]' },
    { bg: 'bg-[#725242]', text: 'text-white', border: 'border-[#725242]' },
    { bg: 'bg-[#EFE3BA]', text: 'text-black', border: 'border-[#8A3E25]' },
    { bg: 'bg-[#FFFFFF]', text: 'text-black', border: 'border-[#25718A]' },
  ]
  const index = category.length % colors.length
  return colors[index]
}

export const NPIProjectDetailsClient: React.FC<NPIProjectDetailsClientProps> = ({ project }) => {
  // Add safety checks for project data
  if (!project) {
    return (
      <NPISection className="py-24 bg-[#FFFFFF] min-h-[70vh] flex items-center">
        <div className="container mx-auto px-4 text-center">
          <h1 className="text-2xl font-bold text-black mb-4">Project Not Found</h1>
          <p className="text-[#725242]">The requested project could not be loaded.</p>
        </div>
      </NPISection>
    )
  }

  const location =
    project.location?.specificLocation ||
    (project.location?.counties && project.location.counties.length > 0
      ? `${project.location.counties.length} Counties`
      : 'Kenya')

  const duration =
    project.timeline?.duration ||
    (project.timeline?.startDate
      ? `Started ${new Date(project.timeline.startDate).getFullYear()}`
      : 'Ongoing')

  const budget = project.budget?.totalBudget
    ? `${project.budget.currency || 'KES'} ${project.budget.totalBudget.toLocaleString()}`
    : 'Budget TBD'

  const partners =
    project.team?.implementingPartners?.map((p: any) =>
      typeof p.partner === 'string' ? p.partner : p.partner?.name || 'Partner',
    ) || []

  // Extract description text from Lexical format
  const descriptionText =
    extractTextFromLexical(project.description) || project.summary || 'No description available.'

  // Handle image URL - supports database-stored images
  const getImageUrl = (image: any): string => {
    if (!image) return '/assets/product 1.jpg'

    // Handle database-stored media
    if (typeof image === 'object') {
      if (image.id && image.storageType === 'database') {
        return `/api/media/database/${image.id}`
      }

      // Handle enhanced image field (group with image property)
      if (image.image) {
        if (typeof image.image === 'object') {
          if (image.image.id && image.image.storageType === 'database') {
            return `/api/media/database/${image.image.id}`
          }
          if (image.image.url) {
            return image.image.url
          }
        }
      }

      // Handle direct URL property
      if (image.url) {
        return image.url
      }
    }

    return '/assets/product 1.jpg'
  }

  const imageUrl = getImageUrl(project.image)

  // Safe access to project properties
  const projectTitle = project.title || 'Untitled Project'
  const projectCategory = project.category || 'general'
  const projectPillar = project.pillar || 'general'
  const projectStatus = project.status || 'active'

  return (
    <>
      {/* Breadcrumb */}
      <NPISection className="py-4 bg-[#EFE3BA] border-b border-[#725242]/20" size="tight">
        <div className="container mx-auto px-4">
          <nav className="flex items-center space-x-2 text-sm">
            <Link href="/" className="text-[#725242] hover:text-[#8A3E25] transition-colors">
              Home
            </Link>
            <span className="text-[#725242]/60">/</span>
            <Link
              href="/projects"
              className="text-[#725242] hover:text-[#8A3E25] transition-colors"
            >
              Projects
            </Link>
            <span className="text-[#725242]/60">/</span>
            <span className="text-[#8A3E25] font-medium truncate max-w-xs">{projectTitle}</span>
          </nav>
        </div>
      </NPISection>

      {/* Hero Section */}
      <NPISection className="relative min-h-[70vh] bg-[#FFFFFF]" container={false}>
        <div className="relative h-[70vh] w-full">
          <Image
            src={imageUrl}
            alt={(project.image && typeof project.image === 'object' && project.image.alt) || projectTitle}
            fill
            className="object-cover"
            priority
          />
          <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-black/40 to-transparent" />

          {/* Back Button */}
          <div className="absolute top-6 left-6 z-10">
            <Link href="/projects">
              <NPIButton
                variant="outline"
                className="bg-white/90 hover:bg-white text-black border-white/20 hover:border-white"
              >
                <ArrowLeft className="w-4 h-4 mr-2" />
                Back to Projects
              </NPIButton>
            </Link>
          </div>

          {/* Hero Content */}
          <div className="absolute bottom-0 left-0 right-0 p-8">
            <div className="container mx-auto">
              <motion.div
                initial={{ opacity: 0, y: 30 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8 }}
                className="max-w-4xl"
              >
                <div className="flex flex-wrap items-center gap-4 mb-6">
                  <span
                    className={`px-4 py-2 text-sm font-medium ${getStatusColor(projectStatus)}`}
                  >
                    {projectStatus.charAt(0).toUpperCase() + projectStatus.slice(1)}
                  </span>
                  <span
                    className={`px-4 py-2 text-sm font-bold ${getCategoryColor(projectCategory).bg} ${getCategoryColor(projectCategory).text} border ${getCategoryColor(projectCategory).border}`}
                  >
                    {projectCategory}
                  </span>
                  <span className="px-4 py-2 text-sm font-medium bg-[#725242] text-white">
                    {projectPillar}
                  </span>
                </div>
                <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold text-white mb-6 leading-tight">
                  {projectTitle}
                </h1>
                <p className="text-xl text-white/90 leading-relaxed max-w-3xl">
                  {project.summary || descriptionText}
                </p>
              </motion.div>
            </div>
          </div>
        </div>
      </NPISection>

      {/* Project Overview */}
      <NPISection className="py-16 bg-[#EFE3BA]">
        <div className="container mx-auto px-4">
          <div className="grid lg:grid-cols-3 gap-8">
            {/* Main Content */}
            <div className="lg:col-span-2 space-y-8">
              <motion.div
                initial={{ opacity: 0, y: 30 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.2 }}
              >
                <NPICard className="bg-[#FFFFFF] border-2 border-[#725242]/20">
                  <NPICardHeader>
                    <NPICardTitle className="text-2xl text-black mb-4">
                      Project Overview
                    </NPICardTitle>
                  </NPICardHeader>
                  <NPICardContent>
                    <div className="prose prose-lg max-w-none text-[#725242]">
                      <p className="leading-relaxed">{descriptionText}</p>

                      {project.objectives && project.objectives.length > 0 && (
                        <div className="mt-6">
                          <h3 className="text-xl font-bold text-black mb-4 flex items-center gap-2">
                            <Target className="w-5 h-5 text-[#8A3E25]" />
                            Key Objectives
                          </h3>
                          <ul className="space-y-3">
                            {project.objectives.map((objective, index) => (
                              <li key={index} className="flex items-start gap-3">
                                <CheckCircle className="w-5 h-5 text-[#25718A] mt-0.5 flex-shrink-0" />
                                <span>{objective}</span>
                              </li>
                            ))}
                          </ul>
                        </div>
                      )}
                    </div>
                  </NPICardContent>
                </NPICard>
              </motion.div>

              {/* Impact & Results */}
              {project.impact && (
                <motion.div
                  initial={{ opacity: 0, y: 30 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: 0.4 }}
                >
                  <NPICard className="bg-[#FFFFFF] border-2 border-[#725242]/20">
                    <NPICardHeader>
                      <NPICardTitle className="text-2xl text-black mb-4 flex items-center gap-2">
                        <TrendingUp className="w-6 h-6 text-[#8A3E25]" />
                        Impact & Results
                      </NPICardTitle>
                    </NPICardHeader>
                    <NPICardContent>
                      <div className="grid md:grid-cols-2 gap-6">
                        {project.impact.beneficiaries && (
                          <div className="text-center p-4 bg-[#EFE3BA] border border-[#725242]/20">
                            <div className="text-3xl font-bold text-[#8A3E25] mb-2">
                              {project.impact.beneficiaries.toLocaleString()}
                            </div>
                            <div className="text-[#725242] font-medium">Beneficiaries</div>
                          </div>
                        )}
                        {project.impact.outcomes && project.impact.outcomes.length > 0 && (
                          <div className="space-y-2">
                            <h4 className="font-bold text-black">Key Outcomes:</h4>
                            <ul className="space-y-1 text-[#725242]">
                              {project.impact.outcomes.map((outcome, index) => (
                                <li key={index} className="flex items-start gap-2">
                                  <span className="w-2 h-2 bg-[#25718A] mt-2 flex-shrink-0"></span>
                                  <span>{outcome}</span>
                                </li>
                              ))}
                            </ul>
                          </div>
                        )}
                      </div>
                    </NPICardContent>
                  </NPICard>
                </motion.div>
              )}
            </div>

            {/* Sidebar */}
            <div className="space-y-6">
              {/* Project Details */}
              <motion.div
                initial={{ opacity: 0, x: 30 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.6, delay: 0.3 }}
              >
                <NPICard className="bg-[#FFFFFF] border-2 border-[#725242]/20">
                  <NPICardHeader>
                    <NPICardTitle className="text-xl text-black">Project Details</NPICardTitle>
                  </NPICardHeader>
                  <NPICardContent>
                    <div className="space-y-4">
                      <div className="flex items-center gap-3">
                        <MapPin className="w-5 h-5 text-[#8A3E25] flex-shrink-0" />
                        <div>
                          <div className="font-medium text-black">Location</div>
                          <div className="text-[#725242]">{location}</div>
                        </div>
                      </div>
                      <div className="flex items-center gap-3">
                        <Calendar className="w-5 h-5 text-[#8A3E25] flex-shrink-0" />
                        <div>
                          <div className="font-medium text-black">Duration</div>
                          <div className="text-[#725242]">{duration}</div>
                        </div>
                      </div>
                      <div className="flex items-center gap-3">
                        <Users className="w-5 h-5 text-[#8A3E25] flex-shrink-0" />
                        <div>
                          <div className="font-medium text-black">Participants</div>
                          <div className="text-[#725242]">
                            {project.impact?.beneficiaries?.toLocaleString() || 'TBD'}
                          </div>
                        </div>
                      </div>
                      <div className="flex items-center gap-3">
                        <DollarSign className="w-5 h-5 text-[#8A3E25] flex-shrink-0" />
                        <div>
                          <div className="font-medium text-black">Budget</div>
                          <div className="text-[#725242]">{budget}</div>
                        </div>
                      </div>
                    </div>
                  </NPICardContent>
                </NPICard>
              </motion.div>

              {/* Partners */}
              {partners.length > 0 && (
                <motion.div
                  initial={{ opacity: 0, x: 30 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.6, delay: 0.5 }}
                >
                  <NPICard className="bg-[#FFFFFF] border-2 border-[#725242]/20">
                    <NPICardHeader>
                      <NPICardTitle className="text-xl text-black flex items-center gap-2">
                        <Handshake className="w-5 h-5 text-[#8A3E25]" />
                        Key Partners
                      </NPICardTitle>
                    </NPICardHeader>
                    <NPICardContent>
                      <div className="space-y-2">
                        {partners.map((partner, index) => (
                          <div
                            key={index}
                            className="flex items-center gap-3 p-3 bg-[#EFE3BA] border border-[#725242]/20"
                          >
                            <Building className="w-4 h-4 text-[#8A3E25] flex-shrink-0" />
                            <span className="text-[#725242] font-medium">{partner}</span>
                          </div>
                        ))}
                      </div>
                    </NPICardContent>
                  </NPICard>
                </motion.div>
              )}
            </div>
          </div>
        </div>
      </NPISection>

      {/* Call to Action */}
      <NPISection className="py-16 bg-[#725242]">
        <div className="container mx-auto px-4 text-center">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.6 }}
            className="max-w-3xl mx-auto"
          >
            <h2 className="text-3xl md:text-4xl font-bold text-white mb-6">
              Interested in Similar Projects?
            </h2>
            <p className="text-white/90 text-lg mb-8 leading-relaxed">
              Explore more of our transformative initiatives or learn how you can get involved in
              Kenya's natural products development journey.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link href="/projects">
                <NPIButton className="bg-[#8A3E25] hover:bg-[#25718A] text-white border-2 border-[#8A3E25] hover:border-[#25718A] px-8 py-3">
                  View All Projects
                </NPIButton>
              </Link>
              <Link href="/partnerships">
                <NPIButton
                  variant="outline"
                  className="border-white text-white hover:bg-white hover:text-[#725242] px-8 py-3"
                >
                  Partner With Us
                </NPIButton>
              </Link>
            </div>
          </motion.div>
        </div>
      </NPISection>
    </>
  )
}
