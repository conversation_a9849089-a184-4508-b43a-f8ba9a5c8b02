import type { CollectionConfig } from 'payload'

import {
  FixedToolbarFeature,
  InlineToolbarFeature,
  lexicalEditor,
} from '@payloadcms/richtext-lexical'
import { vercelBlobStorage } from '@payloadcms/storage-vercel-blob'
import path from 'path'
import { fileURLToPath } from 'url'

import { anyone } from '../access/anyone'
import { authenticated } from '../access/authenticated'
import { generateUniversalId } from '../hooks/generateUniversalId'
import { storeImageInDatabase } from '../hooks/storeImageInDatabase'
import { sanitizeForDatabase } from '../hooks/sanitizeForDatabase'

const filename = fileURLToPath(import.meta.url)
const dirname = path.dirname(filename)

export const Media: CollectionConfig = {
  slug: 'media',
  access: {
    create: authenticated,
    delete: authenticated,
    read: anyone,
    update: authenticated,
  },
  admin: {
    useAsTitle: 'filename',
    defaultColumns: ['filename', 'alt', 'mimeType', 'filesize', 'createdAt'],
    group: 'Content Management',
  },
  fields: [
    {
      name: 'mediaId',
      type: 'text',
      admin: {
        readOnly: true,
        description: 'Auto-generated unique media identifier',
        position: 'sidebar',
      },
      access: {
        create: () => false, // Prevent manual creation
        update: () => false, // Prevent manual updates
      },
    },
    {
      name: 'base64Data',
      type: 'textarea',
      admin: {
        readOnly: true,
        hidden: true, // Hide from admin interface
        description: 'Base64 encoded image data stored in database',
      },
      access: {
        create: () => false, // Prevent manual creation
        update: () => false, // Prevent manual updates
      },
    },
    {
      name: 'originalFilename',
      type: 'text',
      admin: {
        readOnly: true,
        description: 'Original filename when uploaded',
        position: 'sidebar',
      },
      access: {
        create: () => false, // Prevent manual creation
        update: () => false, // Prevent manual updates
      },
    },
    {
      name: 'storageType',
      type: 'select',
      options: [
        { label: 'Database (Base64)', value: 'database' },
        { label: 'File System', value: 'filesystem' },
        { label: 'External Storage', value: 'external' },
      ],
      defaultValue: 'database',
      admin: {
        readOnly: true,
        description: 'How this media file is stored',
        position: 'sidebar',
      },
      access: {
        create: () => false, // Prevent manual creation
        update: () => false, // Prevent manual updates
      },
    },
    {
      name: 'alt',
      type: 'text',
      required: true,
      admin: {
        description: 'Alternative text for accessibility (required for images)',
      },
    },
    {
      name: 'caption',
      type: 'richText',
      editor: lexicalEditor({
        features: ({ rootFeatures }) => {
          return [...rootFeatures, FixedToolbarFeature(), InlineToolbarFeature()]
        },
      }),
      admin: {
        description: 'Optional caption for the media item',
      },
    },
    {
      name: 'description',
      type: 'textarea',
      admin: {
        description: 'Detailed description of the media content',
      },
    },
    {
      name: 'category',
      type: 'select',
      options: [
        { label: 'General', value: 'general' },
        { label: 'Projects', value: 'projects' },
        { label: 'Events', value: 'events' },
        { label: 'News', value: 'news' },
        { label: 'Success Stories', value: 'success-stories' },
        { label: 'Resources', value: 'resources' },
        { label: 'Partners', value: 'partners' },
        { label: 'Team', value: 'team' },
        { label: 'Logos', value: 'logos' },
        { label: 'Documents', value: 'documents' },
      ],
      admin: {
        description: 'Category for organizing media files',
      },
    },
    {
      name: 'tags',
      type: 'array',
      fields: [
        {
          name: 'tag',
          type: 'text',
        },
      ],
      admin: {
        description: 'Tags for search and filtering',
      },
    },
    {
      name: 'credits',
      type: 'group',
      fields: [
        {
          name: 'photographer',
          type: 'text',
          admin: {
            description: 'Photographer or creator name',
          },
        },
        {
          name: 'organization',
          type: 'text',
          admin: {
            description: 'Organization or agency',
          },
        },
        {
          name: 'copyright',
          type: 'text',
          admin: {
            description: 'Copyright information',
          },
        },
        {
          name: 'license',
          type: 'select',
          options: [
            { label: 'All Rights Reserved', value: 'all-rights-reserved' },
            { label: 'Creative Commons CC BY', value: 'cc-by' },
            { label: 'Creative Commons CC BY-SA', value: 'cc-by-sa' },
            { label: 'Creative Commons CC BY-NC', value: 'cc-by-nc' },
            { label: 'Creative Commons CC BY-NC-SA', value: 'cc-by-nc-sa' },
            { label: 'Public Domain', value: 'public-domain' },
          ],
          defaultValue: 'all-rights-reserved',
        },
      ],
    },
    {
      name: 'usage',
      type: 'group',
      fields: [
        {
          name: 'allowPublicUse',
          type: 'checkbox',
          defaultValue: true,
          admin: {
            description: 'Allow public use of this media',
          },
        },
        {
          name: 'requireAttribution',
          type: 'checkbox',
          defaultValue: false,
          admin: {
            description: 'Require attribution when used',
          },
        },
        {
          name: 'commercialUse',
          type: 'checkbox',
          defaultValue: false,
          admin: {
            description: 'Allow commercial use',
          },
        },
      ],
    },
  ],
  upload: {
    // Store files in database instead of external storage
    disableLocalStorage: true, // Disable local file storage
    adminThumbnail: ({ doc }) => {
      // Generate thumbnail from base64 data if available
      if (doc.base64Data) {
        return `data:${doc.mimeType};base64,${doc.base64Data}`
      }
      return doc.url || ''
    },
    focalPoint: true,
    mimeTypes: [
      // Images
      'image/jpeg',
      'image/jpg',
      'image/png',
      'image/gif',
      'image/webp',
      'image/svg+xml',
      // Documents
      'application/pdf',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'application/vnd.ms-excel',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'application/vnd.ms-powerpoint',
      'application/vnd.openxmlformats-officedocument.presentationml.presentation',
      'text/plain',
      'text/csv',
      // Audio
      'audio/mpeg',
      'audio/wav',
      'audio/ogg',
      'audio/mp4',
      // Video
      'video/mp4',
      'video/mpeg',
      'video/quicktime',
      'video/webm',
      'video/ogg',
    ],
    imageSizes: [
      {
        name: 'thumbnail',
        width: 300,
        height: 300,
        crop: 'center',
      },
      {
        name: 'square',
        width: 500,
        height: 500,
        crop: 'center',
      },
      {
        name: 'small',
        width: 600,
        height: 400,
        crop: 'center',
      },
      {
        name: 'medium',
        width: 900,
        height: 600,
        crop: 'center',
      },
      {
        name: 'large',
        width: 1400,
        height: 900,
        crop: 'center',
      },
      {
        name: 'xlarge',
        width: 1920,
        height: 1080,
        crop: 'center',
      },
      {
        name: 'hero',
        width: 1920,
        height: 800,
        crop: 'center',
      },
      {
        name: 'og',
        width: 1200,
        height: 630,
        crop: 'center',
      },
      {
        name: 'card',
        width: 400,
        height: 300,
        crop: 'center',
      },
    ],
    formatOptions: {
      format: 'webp',
      options: {
        quality: 85,
      },
    },
  },
  hooks: {
    beforeChange: [generateUniversalId, storeImageInDatabase, sanitizeForDatabase],
  },
}
